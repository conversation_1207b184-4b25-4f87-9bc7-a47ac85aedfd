<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>狙击枪射击慢镜头效果最终优化版</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            overflow: hidden;
            background: linear-gradient(to bottom, #0c1445, #1a237e);
            font-family: 'Arial', sans-serif;
            color: white;
            height: 100vh;
            display: flex;
            flex-direction: column;
            perspective: 1000px;
        }
        
        header {
            padding: 15px;
            text-align: center;
            background: rgba(0, 0, 0, 0.5);
            border-bottom: 1px solid rgba(255, 0, 0, 0.3);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
            z-index: 100;
            transition: opacity 0.5s ease;
        }
        
        header.hidden {
            opacity: 0;
            pointer-events: none;
        }
        
        h1 {
            font-size: 2.5rem;
            text-shadow: 0 0 15px rgba(255, 50, 50, 0.8);
            margin-bottom: 5px;
            letter-spacing: 2px;
            background: linear-gradient(to right, #ff8a00, #ff2070);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            color: #a0c4ff;
        }
        
        .weapon-info {
            font-size: 1rem;
            color: #ffd700;
            margin-top: 5px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
        
        #container {
            flex: 1;
            position: relative;
            overflow: hidden;
        }
        
        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .panel {
            position: absolute;
            background: rgba(10, 10, 40, 0.8);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
            transition: all 0.5s ease;
            z-index: 10;
        }
        
        .controls {
            bottom: 30px;
            left: 30px;
            border: 1px solid rgba(255, 100, 100, 0.3);
            max-width: 350px;
        }
        
        .controls.hidden {
            transform: translateX(-150%);
            opacity: 0;
        }
        
        .btn {
            background: linear-gradient(to bottom, #ff416c, #e53935);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            margin-top: 15px;
            display: block;
            width: 100%;
            font-weight: bold;
            letter-spacing: 1.5px;
            text-transform: uppercase;
            box-shadow: 0 5px 15px rgba(229, 57, 53, 0.4);
            position: relative;
            overflow: hidden;
        }
        
        .btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: 0.5s;
        }
        
        .btn:hover {
            background: linear-gradient(to bottom, #ff4b2b, #ff416c);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(229, 57, 53, 0.6);
        }
        
        .btn:hover:before {
            left: 100%;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .btn-secondary {
            background: linear-gradient(to bottom, #2196F3, #0D47A1);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }
        
        .info-panel {
            top: 30px;
            right: 30px;
            max-width: 350px;
            border: 1px solid rgba(100, 150, 255, 0.3);
        }
        
        .info-panel.hidden {
            transform: translateX(150%);
            opacity: 0;
        }
        
        .info-panel h3 {
            margin-bottom: 15px;
            color: #ff9800;
            font-size: 1.5rem;
            text-align: center;
            text-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
        }
        
        .info-panel ul {
            padding-left: 0;
            list-style: none;
        }
        
        .info-panel li {
            margin-bottom: 12px;
            line-height: 1.5;
            color: #e0f7fa;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
        }
        
        .info-panel li:before {
            content: '•';
            color: #ff5252;
            margin-right: 10px;
            font-size: 1.5rem;
        }
        
        .key-highlight {
            background: rgba(255, 152, 0, 0.3);
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin: 0 4px;
            color: #ffd700;
            border: 1px solid rgba(255, 152, 0, 0.5);
        }

        #scopeOverlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, transparent 40%, rgba(0,0,0,0.95) 50%);
            pointer-events: none;
            z-index: 49;
            display: none;
        }

        #scopeCrosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 400px;
            height: 400px;
            border: 3px solid rgba(255, 50, 50, 0.8);
            border-radius: 50%;
            pointer-events: none;
            z-index: 50;
            display: none;
            box-shadow: 
                inset 0 0 50px rgba(255, 0, 0, 0.3),
                0 0 30px rgba(255, 0, 0, 0.5);
        }
        
        #scopeCrosshair:before, #scopeCrosshair:after {
            content: '';
            position: absolute;
            background: rgba(255, 50, 50, 0.8);
        }
        
        #scopeCrosshair:before {
            width: 2px;
            height: 100%;
            left: 50%;
            transform: translateX(-50%);
        }
        
        #scopeCrosshair:after {
            height: 2px;
            width: 100%;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .scope-dot {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 6px;
            background: rgba(255, 50, 50, 0.9);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 15px rgba(255, 0, 0, 0.8);
        }
        
        .scope-range {
            position: absolute;
            top: 60%;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
        }
        
        .status-indicator {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.6);
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 1.2rem;
            z-index: 100;
            border: 1px solid rgba(255, 100, 100, 0.3);
            display: none;
        }
        
        .view-indicator {
            top: 80px;
        }
        
        .distance-display {
            top: 130px;
        }
        
        .bullet-time-indicator {
            top: 180px;
            padding: 10px 30px;
            font-size: 1.5rem;
            border: 2px solid rgba(255, 200, 0, 0.8);
            color: #ffcc00;
            text-shadow: 0 0 10px rgba(255, 200, 0, 0.8);
        }
        
        .status-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 5rem;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(255, 0, 0, 0.8);
            opacity: 0;
            z-index: 200;
            pointer-events: none;
            transition: opacity 0.5s;
        }
        #impactText { color: #ff5252; }
        #missText { color: #88aaff; }
        
        .loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #0c1445;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            transition: opacity 1s;
        }
        
        .loading-bar {
            width: 300px;
            height: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            margin-top: 20px;
            overflow: hidden;
        }
        
        .loading-progress {
            height: 100%;
            width: 0%;
            background: linear-gradient(to right, #ff416c, #ff8a00);
            border-radius: 5px;
            transition: width 0.5s;
        }
        
        .loading-text {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #a0c4ff;
        }
        
        .copyright {
            position: absolute;
            bottom: 15px;
            right: 15px;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.5);
            z-index: 100;
            transition: opacity 0.5s ease;
        }
        
        .copyright.hidden {
            opacity: 0;
        }
        
        .aim-controls {
            bottom: 30px;
            right: 30px;
            border: 1px solid rgba(100, 150, 255, 0.3);
        }
        
        .aim-controls.hidden {
            transform: translateY(150%);
            opacity: 0;
        }
        
        .aim-controls p {
            margin-bottom: 10px;
            color: #a0c4ff;
            text-align: center;
        }
        
        .aim-buttons {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 5px;
            margin-top: 10px;
        }
        
        .aim-btn {
            background: rgba(100, 150, 255, 0.3);
            border: 1px solid rgba(100, 150, 255, 0.5);
            color: white;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 20px;
        }
        
        .aim-btn:hover {
            background: rgba(100, 150, 255, 0.5);
            transform: scale(1.1);
        }
        
        .aim-btn:nth-child(5) {
            grid-column: 2;
        }
        
        .zoom-controls {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 10px;
        }

        .camera-controls {
            position: absolute;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(10, 10, 40, 0.8);
            padding: 10px;
            border-radius: 10px;
            z-index: 101;
            display: flex;
            gap: 10px;
            transition: opacity 0.5s ease;
        }
        .camera-controls.hidden {
            opacity: 0;
            pointer-events: none;
        }
        .cam-btn {
            background: rgba(100, 150, 255, 0.5);
            border: 1px solid rgba(100, 150, 255, 0.7);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .cam-btn:hover {
            background: rgba(100, 150, 255, 0.8);
        }
        .cam-btn.active {
            background: #ff8a00;
            border-color: #ffc107;
            box-shadow: 0 0 10px #ff8a00;
        }
    </style>
</head>
<body>
    <div class="loading">
        <div class="loading-text">正在加载狙击模拟系统...</div>
        <div class="loading-bar">
            <div class="loading-progress" id="loadingProgress"></div>
        </div>
    </div>
    
    <header id="header">
        <h1>狙击枪射击慢镜头模拟</h1>
        <div class="subtitle">子弹飞行轨迹与击中效果高级模拟</div>
        <div class="weapon-info">武器: Barrett M82A1 .50口径反器材狙击步枪</div>
    </header>
    
    <div id="container">
        <div id="canvas-container"></div>
        
        <div id="scopeOverlay"></div>
        <div id="scopeCrosshair">
            <div class="scope-dot"></div>
            <div class="scope-range">200m</div>
        </div>
        
        <div class="camera-controls hidden" id="cameraControls">
            <button class="cam-btn active" onclick="game.setCameraView(0, this)">跟随</button>
            <button class="cam-btn" onclick="game.setCameraView(1, this)">侧视</button>
            <button class="cam-btn" onclick="game.setCameraView(2, this)">特写</button>
            <button class="cam-btn" onclick="game.setCameraView(3, this)">目标</button>
        </div>
        <div class="status-indicator view-indicator" id="viewIndicator"></div>
        <div class="status-indicator distance-display" id="distanceDisplay">距离目标: 0m</div>
        <div class="status-indicator bullet-time-indicator" id="bulletTimeIndicator">子弹时间</div>
        <div class="status-text" id="impactText">命中!</div>
        <div class="status-text" id="missText">未命中</div>
        
        <div class="panel info-panel" id="infoPanel">
            <h3>操作说明</h3>
            <ul>
                <li><span class="key-highlight">↑ ↓ ← →</span>方向键移动瞄准点</li>
                <li><span class="key-highlight">空格键</span>发射子弹</li>
                <li><span class="key-highlight">鼠标滚轮</span>拉近或拉远瞄准镜</li>
                <li><span class="key-highlight">数字键 1-4</span>切换视角</li>
                <li><span class="key-highlight">R 键</span>快速重置场景</li>
                <li>命中目标后自动重置</li>
            </ul>
        </div>
        
        <div class="panel controls" id="controls">
            <p>点击下方按钮进入瞄准模式：</p>
            <button id="fireBtn" class="btn">进入瞄准</button>
            <button id="resetBtn" class="btn btn-secondary">重置场景</button>
        </div>
        
        <div class="panel aim-controls hidden" id="aimControls">
            <p>方向键/按钮瞄准</p>
            <div class="aim-buttons">
                <div></div>
                <button class="aim-btn" onclick="game.adjustAim('up')">↑</button>
                <div></div>
                <button class="aim-btn" onclick="game.adjustAim('left')">←</button>
                <button class="aim-btn" onclick="game.fireActualBullet()">🎯</button>
                <button class="aim-btn" onclick="game.adjustAim('right')">→</button>
                <div></div>
                <button class="aim-btn" onclick="game.adjustAim('down')">↓</button>
                <div></div>
            </div>
            <p>缩放</p>
            <div class="zoom-controls">
                <button class="aim-btn" onclick="game.adjustZoom(-1)">-</button>
                <button class="aim-btn" onclick="game.adjustZoom(1)">+</button>
            </div>
        </div>
        
        <div class="copyright" id="copyright">Three.js 狙击模拟系统 © 2023</div>
    </div>

    <script>
        class SniperGame {
            constructor() {
                // 场景相关
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                
                // 游戏对象
                this.bullet = null;
                this.sniperRifle = null;
                this.targets = [];
                this.particles = [];
                this.bulletTrail = [];
                
                // 游戏状态
                this.bulletFired = false;
                this.inAimMode = false;
                this.aimOffset = { x: 0, y: 0 };
                this.currentCameraView = 0;
                
                // 常量配置
                this.config = {
                    bulletSpeed: 0.8,
                    trailLength: 60,
                    initialFOV: 60,
                    minFOV: 10,
                    maxFOV: 60,
                    viewNames: ['跟随视角', '平行侧视', '子弹特写', '目标追踪']
                };
                
                // 工具对象
                this.clock = new THREE.Clock();
                
                // UI元素缓存
                this.uiElements = {};
                this.cacheUIElements();
            }
            
            cacheUIElements() {
                const ids = [
                    'loadingProgress', 'header', 'scopeCrosshair', 'scopeOverlay',
                    'cameraControls', 'viewIndicator', 'distanceDisplay', 
                    'bulletTimeIndicator', 'impactText', 'missText', 'infoPanel',
                    'controls', 'copyright', 'aimControls', 'fireBtn', 'resetBtn'
                ];
                
                ids.forEach(id => {
                    this.uiElements[id] = document.getElementById(id);
                });
            }
            
            toggleUIVisibility(elements, show) {
                elements.forEach(el => {
                    if (typeof el === 'string') el = this.uiElements[el];
                    if (el) el.classList[show ? 'remove' : 'add']('hidden');
                });
            }
            
            setUIDisplay(elements, display) {
                elements.forEach(el => {
                    if (typeof el === 'string') el = this.uiElements[el];
                    if (el) el.style.display = display;
                });
            }
            
            init() {
                this.simulateLoading();
                this.setupScene();
                this.setupLighting();
                this.createEnvironment();
                this.createSniperRifle();
                this.createTargets();
                this.setupEventListeners();
                this.animate();
            }
            
            setupScene() {
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x0a0a2a);
                this.scene.fog = new THREE.Fog(0x0a0a2a, 100, 400);
                
                this.camera = new THREE.PerspectiveCamera(
                    this.config.initialFOV,
                    window.innerWidth / window.innerHeight,
                    0.1,
                    3000
                );
                this.camera.position.set(0, 1.5, 10);
                
                this.renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                this.renderer.setSize(window.innerWidth, window.innerHeight);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
                this.renderer.toneMappingExposure = 1.5;
                document.getElementById('canvas-container').appendChild(this.renderer.domElement);
            }
            
            setupLighting() {
                const ambientLight = new THREE.AmbientLight(0x404040, 1.5);
                this.scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
                directionalLight.position.set(5, 10, 7);
                directionalLight.castShadow = true;
                this.scene.add(directionalLight);
            }
            
            createEnvironment() {
                // 地面
                const groundGeometry = new THREE.PlaneGeometry(500, 500, 50, 50);
                const groundMaterial = new THREE.MeshStandardMaterial({ 
                    color: 0x2e7d32, 
                    roughness: 0.9 
                });
                const ground = new THREE.Mesh(groundGeometry, groundMaterial);
                ground.rotation.x = -Math.PI / 2;
                ground.position.y = -1;
                ground.receiveShadow = true;
                this.scene.add(ground);
                
                // 岩石
                const rockGeometry = new THREE.DodecahedronGeometry(1, 1);
                const rockMaterial = new THREE.MeshStandardMaterial({ 
                    color: 0x7f7f7f, 
                    roughness: 0.9 
                });
                
                for (let i = 0; i < 30; i++) {
                    const rock = new THREE.Mesh(rockGeometry, rockMaterial);
                    rock.position.set(
                        Math.random() * 200 - 100,
                        -1 + Math.random() * 0.5,
                        -50 - Math.random() * 300
                    );
                    rock.scale.set(
                        0.5 + Math.random(),
                        0.3 + Math.random() * 0.7,
                        0.5 + Math.random()
                    );
                    rock.castShadow = true;
                    this.scene.add(rock);
                }
            }
            
            createSniperRifle() {
                const rifleGroup = new THREE.Group();
                
                // 枪管
                const barrelMaterial = new THREE.MeshStandardMaterial({ 
                    color: 0x1a1a1a, 
                    metalness: 0.95, 
                    roughness: 0.1 
                });
                const barrel = new THREE.Mesh(
                    new THREE.CylinderGeometry(0.08, 0.12, 4, 16),
                    barrelMaterial
                );
                barrel.rotation.z = Math.PI / 2;
                barrel.position.set(-2, 0, 0);
                rifleGroup.add(barrel);
                
                // 瞄准镜
                const scopeGroup = new THREE.Group();
                const scopeMaterial = new THREE.MeshStandardMaterial({ 
                    color: 0x0a0a0a, 
                    metalness: 0.95, 
                    roughness: 0.05 
                });
                const scopeBody = new THREE.Mesh(
                    new THREE.CylinderGeometry(0.2, 0.2, 2, 32),
                    scopeMaterial
                );
                scopeBody.rotation.z = Math.PI / 2;
                scopeGroup.add(scopeBody);
                scopeGroup.position.set(0, 0.5, 0);
                rifleGroup.add(scopeGroup);
                
                rifleGroup.position.set(0.3, -0.2, -1);
                rifleGroup.rotation.y = -0.05;
                this.camera.add(rifleGroup);
                this.scene.add(this.camera);
                this.sniperRifle = rifleGroup;
            }
            
            createTargets() {
                // 清理旧目标
                this.targets.forEach(t => this.scene.remove(t.mesh));
                this.targets = [];

                const targetData = [
                    { 
                        type: 'cylinder', 
                        color: 0xf5f5f5, 
                        position: new THREE.Vector3(0, 4, -200), 
                        size: { x: 6, y: 0.6, z: 6 } 
                    },
                    { 
                        type: 'box', 
                        color: 0xffa726, 
                        position: new THREE.Vector3(-20, 8, -190), 
                        size: { x: 5, y: 5, z: 5 } 
                    },
                    { 
                        type: 'sphere', 
                        color: 0x42a5f5, 
                        position: new THREE.Vector3(25, 10, -210), 
                        size: { x: 4, y: 4, z: 4 } 
                    }
                ];

                targetData.forEach(data => {
                    const geometry = this.createTargetGeometry(data.type, data.size);
                    const material = new THREE.MeshStandardMaterial({ 
                        color: data.color, 
                        roughness: 0.7 
                    });
                    const mesh = new THREE.Mesh(geometry, material);
                    mesh.position.copy(data.position);
                    if (data.type === 'cylinder') mesh.rotation.x = Math.PI / 2;
                    mesh.castShadow = true;
                    mesh.receiveShadow = true;
                    
                    this.scene.add(mesh);
                    this.targets.push({ mesh: mesh, size: data.size.x });
                });
            }
            
            createTargetGeometry(type, size) {
                switch(type) {
                    case 'box':
                        return new THREE.BoxGeometry(size.x, size.y, size.z);
                    case 'sphere':
                        return new THREE.SphereGeometry(size.x, 32, 32);
                    case 'cylinder':
                    default:
                        return new THREE.CylinderGeometry(size.x, size.z, size.y, 32);
                }
            }
            
            setupEventListeners() {
                this.uiElements.fireBtn.addEventListener('click', () => this.enterAimMode());
                this.uiElements.resetBtn.addEventListener('click', () => this.resetScene());
                window.addEventListener('resize', () => this.onWindowResize());
                window.addEventListener('keydown', (e) => this.handleKeyPress(e));
                window.addEventListener('wheel', (e) => this.handleMouseWheel(e), { passive: false });
            }
            
            handleMouseWheel(event) {
                if (!this.inAimMode || this.bulletFired) return;
                
                event.preventDefault();
                const delta = event.deltaY > 0 ? -1 : 1;
                this.adjustZoom(delta);
            }
            
            simulateLoading() {
                let progress = 0;
                const loadingScreen = document.querySelector('.loading');
                
                const interval = setInterval(() => {
                    progress += Math.random() * 15;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        setTimeout(() => {
                            loadingScreen.style.opacity = '0';
                            setTimeout(() => loadingScreen.style.display = 'none', 1000);
                        }, 500);
                    }
                    this.uiElements.loadingProgress.style.width = `${progress}%`;
                }, 100);
            }
            
            enterAimMode() {
                this.inAimMode = true;
                this.uiElements.fireBtn.disabled = true;
                
                this.setUIDisplay(['scopeCrosshair', 'scopeOverlay'], 'block');
                this.toggleUIVisibility(['controls', 'infoPanel', 'header', 'copyright'], false);
                this.toggleUIVisibility(['aimControls'], true);
                
                this.adjustZoom(1, true);
            }
            
            adjustAim(direction) {
                if (!this.inAimMode) return;
                
                const aimSpeed = 0.5;
                const directions = {
                    'up': { x: 0, y: aimSpeed },
                    'down': { x: 0, y: -aimSpeed },
                    'left': { x: -aimSpeed, y: 0 },
                    'right': { x: aimSpeed, y: 0 }
                };
                
                const dir = directions[direction];
                this.aimOffset.x += dir.x;
                this.aimOffset.y += dir.y;
                
                this.aimOffset.x = THREE.MathUtils.clamp(this.aimOffset.x, -30, 30);
                this.aimOffset.y = THREE.MathUtils.clamp(this.aimOffset.y, -20, 20);
                
                this.camera.lookAt(this.aimOffset.x, 4 + this.aimOffset.y, -200);
                
                if (this.sniperRifle) {
                    this.sniperRifle.rotation.y = -0.05 + this.aimOffset.x * 0.01;
                    this.sniperRifle.rotation.x = this.aimOffset.y * 0.01;
                }
            }
            
            adjustZoom(direction, isInitial = false) {
                if (!this.inAimMode) return;
                
                const zoomStep = 5;
                if (isInitial) {
                    this.camera.fov = this.config.initialFOV / 2;
                } else {
                    this.camera.fov -= direction * zoomStep;
                }
                
                this.camera.fov = THREE.MathUtils.clamp(
                    this.camera.fov, 
                    this.config.minFOV, 
                    this.config.maxFOV
                );
                this.camera.updateProjectionMatrix();
                
                // 更新瞄准镜距离显示
                const zoomLevel = (this.config.maxFOV - this.camera.fov) / (this.config.maxFOV - this.config.minFOV);
                const distance = Math.round(200 - zoomLevel * 150);
                document.querySelector('.scope-range').textContent = `${distance}m`;
            }
            
            handleKeyPress(event) {
                // 全局快捷键
                if (event.key.toLowerCase() === 'r') {
                    this.resetScene();
                    return;
                }
                
                // 数字键切换视角（子弹飞行时）
                if (this.bulletFired && !this.inAimMode) {
                    const viewMap = {
                        '1': 0,
                        '2': 1,
                        '3': 2,
                        '4': 3
                    };
                    if (viewMap.hasOwnProperty(event.key)) {
                        this.setCameraView(viewMap[event.key]);
                        return;
                    }
                }
                
                if (!this.inAimMode || this.bulletFired) return;
                
                const keyActions = {
                    'ArrowUp': () => this.adjustAim('up'),
                    'ArrowDown': () => this.adjustAim('down'),
                    'ArrowLeft': () => this.adjustAim('left'),
                    'ArrowRight': () => this.adjustAim('right'),
                    ' ': () => this.fireActualBullet(),
                    'Enter': () => this.fireActualBullet(),
                    '+': () => this.adjustZoom(1),
                    '=': () => this.adjustZoom(1),
                    '-': () => this.adjustZoom(-1),
                    '_': () => this.adjustZoom(-1)
                };
                
                const action = keyActions[event.key];
                if (action) action();
            }
            
            fireActualBullet() {
                if (this.bulletFired) return;
                
                this.bulletFired = true;
                this.inAimMode = false;
                this.sniperRifle.visible = false;
                
                this.toggleUIVisibility(['aimControls'], false);
                this.setUIDisplay(['scopeCrosshair', 'scopeOverlay'], 'none');
                this.setUIDisplay(['distanceDisplay', 'bulletTimeIndicator', 'viewIndicator'], 'block');
                this.toggleUIVisibility(['cameraControls'], true);
                
                this.setCameraView(0);
                this.createBullet();
                this.createBulletTrail();
            }
            
            createBullet() {
                const bulletGeometry = new THREE.SphereGeometry(0.08, 16, 16);
                const bulletMaterial = new THREE.MeshPhongMaterial({ 
                    color: 0xffcc00, 
                    emissive: 0xffaa00, 
                    emissiveIntensity: 0.5 
                });
                this.bullet = new THREE.Mesh(bulletGeometry, bulletMaterial);
                
                const startPosition = new THREE.Vector3();
                this.camera.getWorldPosition(startPosition);
                const direction = new THREE.Vector3();
                this.camera.getWorldDirection(direction);
                
                this.bullet.position.copy(startPosition).add(direction.multiplyScalar(2));
                this.bullet.userData.velocity = direction.clone().multiplyScalar(this.config.bulletSpeed);
                this.scene.add(this.bullet);
            }
            
            createBulletTrail() {
                const trailMaterial = new THREE.MeshBasicMaterial({ 
                    color: 0xffaa00, 
                    transparent: true, 
                    opacity: 0.7 
                });
                
                for (let i = 0; i < this.config.trailLength; i++) {
                    const trailPoint = new THREE.Mesh(
                        new THREE.SphereGeometry(0.03, 8, 8),
                        trailMaterial.clone()
                    );
                    trailPoint.visible = false;
                    this.scene.add(trailPoint);
                    this.bulletTrail.push(trailPoint);
                }
            }
            
            updateBulletTrail() {
                if (!this.bulletFired || !this.bullet) return;
                
                const newPoint = this.bulletTrail.shift();
                newPoint.position.copy(this.bullet.position);
                newPoint.visible = true;
                this.bulletTrail.push(newPoint);
                
                for (let i = 0; i < this.bulletTrail.length; i++) {
                    this.bulletTrail[i].material.opacity = 0.7 * (i / this.bulletTrail.length);
                }
            }
            
            setCameraView(viewIndex, buttonElement) {
                this.currentCameraView = viewIndex;
                this.uiElements.viewIndicator.textContent = this.config.viewNames[viewIndex];
                
                const buttons = document.querySelectorAll('.cam-btn');
                buttons.forEach(btn => btn.classList.remove('active'));
                
                if (buttonElement) {
                    buttonElement.classList.add('active');
                } else {
                    buttons[viewIndex].classList.add('active');
                }
            }
            
            updateCamera() {
                if (!this.bullet) return;
                
                const elapsedTime = this.clock.getElapsedTime();
                
                switch(this.currentCameraView) {
                    case 0: // 跟随视角
                        this.camera.position.set(
                            this.bullet.position.x,
                            this.bullet.position.y + 0.3,
                            this.bullet.position.z + 5
                        );
                        this.camera.lookAt(this.bullet.position);
                        break;
                    case 1: // 平行侧视
                        this.camera.position.set(
                            this.bullet.position.x + 20,
                            this.bullet.position.y + 2,
                            this.bullet.position.z
                        );
                        this.camera.lookAt(this.bullet.position);
                        break;
                    case 2: // 子弹特写
                        const radius = 2;
                        this.camera.position.set(
                            this.bullet.position.x + Math.cos(elapsedTime * 2) * radius,
                            this.bullet.position.y + Math.sin(elapsedTime * 2) * radius * 0.5,
                            this.bullet.position.z + 2
                        );
                        this.camera.lookAt(this.bullet.position);
                        break;
                    case 3: // 目标追踪
                        if (this.targets.length > 0) {
                            this.camera.position.set(
                                this.targets[0].mesh.position.x,
                                this.targets[0].mesh.position.y + 5,
                                this.targets[0].mesh.position.z + 10
                            );
                            this.camera.lookAt(this.bullet.position);
                        }
                        break;
                }
            }
            
            checkCollisions() {
                if (!this.bullet) return;
                
                for (let i = this.targets.length - 1; i >= 0; i--) {
                    const target = this.targets[i];
                    const distance = this.bullet.position.distanceTo(target.mesh.position);
                    
                    if (distance < target.size) {
                        this.createExplosion(this.bullet.position.clone());
                        this.scene.remove(this.bullet);
                        this.bullet = null;
                        
                        this.scene.remove(target.mesh);
                        this.targets.splice(i, 1);
                        
                        this.showStatusText('impactText');
                        setTimeout(() => this.resetScene(), 5000);
                        return true;
                    }
                }
                
                // 检测未命中
                if (this.bullet.position.z < -250) {
                    this.showStatusText('missText');
                    this.scene.remove(this.bullet);
                    this.bullet = null;
                    setTimeout(() => this.resetScene(), 2000);
                    return false;
                }
            }
            
            createExplosion(position) {
                const shockwave = new THREE.Mesh(
                    new THREE.TorusGeometry(1, 0.3, 16, 32),
                    new THREE.MeshBasicMaterial({ 
                        color: 0xff5500, 
                        transparent: true, 
                        opacity: 0.8, 
                        side: THREE.DoubleSide 
                    })
                );
                shockwave.position.copy(position);
                shockwave.rotation.x = Math.PI / 2;
                shockwave.userData = { life: 1.0, expansionSpeed: 0.5 };
                this.scene.add(shockwave);
                this.particles.push(shockwave);
            }
            
            updateParticles() {
                for (let i = this.particles.length - 1; i >= 0; i--) {
                    const p = this.particles[i];
                    if (p.userData.life !== undefined) {
                        p.userData.life -= 0.015;
                        if (p.material.opacity !== undefined) {
                            p.material.opacity = Math.max(0, p.userData.life);
                        }
                        if (p.userData.expansionSpeed) {
                            const scale = 1 + (1 - p.userData.life) * p.userData.expansionSpeed * 20;
                            p.scale.set(scale, scale, scale);
                        }
                        if (p.userData.life <= 0) {
                            this.scene.remove(p);
                            this.particles.splice(i, 1);
                        }
                    }
                }
            }
            
            showStatusText(textId) {
                const element = this.uiElements[textId];
                element.style.opacity = '1';
                setTimeout(() => element.style.opacity = '0', 3000);
            }
            
            resetScene() {
                // 重置状态
                this.bulletFired = false;
                this.inAimMode = false;
                this.aimOffset = { x: 0, y: 0 };
                this.currentCameraView = 0;
                
                // 清理对象
                if (this.bullet) {
                    this.scene.remove(this.bullet);
                    this.bullet = null;
                }
                
                this.bulletTrail.forEach(p => this.scene.remove(p));
                this.bulletTrail = [];
                this.particles.forEach(p => this.scene.remove(p));
                this.particles = [];
                
                // 重置相机
                this.camera.fov = this.config.initialFOV;
                this.camera.updateProjectionMatrix();
                this.camera.position.set(0, 1.5, 10);
                this.camera.lookAt(0, 4, -200);
                
                // 重置狙击枪
                this.sniperRifle.visible = true;
                if (this.sniperRifle) {
                    this.sniperRifle.rotation.x = 0;
                    this.sniperRifle.rotation.y = -0.05;
                }
                
                // 重置瞄准镜显示
                document.querySelector('.scope-range').textContent = '200m';
                
                // 重置UI
                this.uiElements.fireBtn.disabled = false;
                this.setUIDisplay([
                    'scopeCrosshair', 'scopeOverlay', 'viewIndicator',
                    'distanceDisplay', 'bulletTimeIndicator'
                ], 'none');
                
                this.uiElements.impactText.style.opacity = '0';
                this.uiElements.missText.style.opacity = '0';
                
                this.toggleUIVisibility(['cameraControls', 'aimControls'], false);
                this.toggleUIVisibility(['controls', 'infoPanel', 'header', 'copyright'], true);
                
                // 重新创建目标
                this.createTargets();
            }
            
            onWindowResize() {
                this.camera.aspect = window.innerWidth / window.innerHeight;
                this.camera.updateProjectionMatrix();
                this.renderer.setSize(window.innerWidth, window.innerHeight);
            }
            
            animate() {
                requestAnimationFrame(() => this.animate());
                
                const elapsedTime = this.clock.getElapsedTime();
                
                this.updateParticles();
                
                if (this.bulletFired && this.bullet) {
                    this.bullet.position.add(this.bullet.userData.velocity);
                    this.updateBulletTrail();
                    
                    // 更新距离显示
                    if (this.targets.length > 0) {
                        const distance = this.bullet.position.distanceTo(this.targets[0].mesh.position);
                        this.uiElements.distanceDisplay.textContent = `距离目标: ${Math.round(distance)}m`;
                    }
                    
                    this.updateCamera();
                    this.checkCollisions();
                }
                
                // 瞄准时的呼吸效果
                if (this.inAimMode && this.sniperRifle) {
                    const breathingOffset = Math.sin(elapsedTime * 2) * 0.005;
                    this.camera.position.y = 1.5 + breathingOffset;
                }
                
                this.renderer.render(this.scene, this.camera);
            }
        }
        
        // 创建游戏实例
        const game = new SniperGame();
        window.addEventListener('load', () => game.init());
    </script>
</body>
</html>